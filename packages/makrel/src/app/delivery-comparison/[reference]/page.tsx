import React from 'react'
import { get } from '@/lib/api/torsk-api-server'
import type { DeliveryComparisonDetailType } from '@mercanto/api-types'
import { ErrorPage } from '@/app/_components/ErrorPage'
import DetailTable from '@/app/_components/DetailsTable'
import { formatDateDisplay, formatDateTimeDisplay } from '@/utils/date'
import PageHeading from '@/app/_components/PageHeading'
import { formatNumber } from '@/utils/number'

type PageProps = {
  params: Promise<{ reference: string }>
  searchParams: Promise<Record<string, string>>
}

export default async function DeliveryComparisonDetailPage({
  params,
  searchParams
}: PageProps) {
  const resolvedParams = await params
  const resolvedSearchParams = await searchParams

  const { reference } = resolvedParams
  const backUrl = resolvedSearchParams.backUrl
    ? decodeURIComponent(resolvedSearchParams.backUrl)
    : '/sales-orders'

  let deliveryComparison = null
  let error = null

  try {
    const response = await get<DeliveryComparisonDetailType>(
      `/v1/delivery-comparison/${reference}`
    )

    if (response.error) {
      error = response.error
    } else {
      deliveryComparison = response.data
    }
  } catch (err) {
    error =
      err instanceof Error
        ? err
        : new Error('Failed to fetch delivery comparison details')
  }

  if (error) {
    return <ErrorPage error={error} title="Error Loading Delivery Comparison" />
  }

  if (!deliveryComparison) {
    return (
      <ErrorPage
        error={new Error('Delivery comparison not found')}
        title="Delivery Comparison Not Found"
      />
    )
  }

  if (!deliveryComparison.isValid) {
    return (
      <main>
        <PageHeading backLink={{ title: 'Sales Orders', href: backUrl }}>
          Delivery Comparison for reference &quot;{reference}&quot;
        </PageHeading>
        <div className="rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-red-800">
            Sales Order for reference &apos;{reference}&apos; is not in a valid
            sales order IoCanto format.
          </p>
        </div>
      </main>
    )
  }

  const { header, positions } = deliveryComparison

  if (!header || !positions) {
    return (
      <ErrorPage
        error={new Error('Invalid delivery comparison data')}
        title="Invalid Data"
      />
    )
  }

  const headerParams = [
    { label: 'Merchant', value: header.merchant },
    { label: 'Customer Key', value: header.customerKey },
    { label: 'Customer Type', value: header.customerType },
    { label: 'Order Reference', value: header.orderReference },
    { label: 'Order Date', value: formatDateDisplay(header.orderDate) },
    { label: 'Last Update', value: formatDateTimeDisplay(header.lastUpdate) }
  ]

  const renderStatisticTable = (name: string, statistic: any) => (
    <div className="mb-6">
      <h3 className="mb-2 text-center text-lg font-semibold">{name}</h3>
      <table className="w-full border-collapse border border-gray-300">
        <thead>
          <tr className="bg-gray-100">
            <th className="border border-gray-300 px-4 py-2">Mercanto</th>
            <th className="border border-gray-300 px-4 py-2">Confirmed</th>
            <th className="border border-gray-300 px-4 py-2">Outstanding</th>
            <th className="border border-gray-300 px-4 py-2">Difference</th>
            <th className="border border-gray-300 px-4 py-2">Cancelled</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td className="border border-gray-300 px-4 py-2 text-center">
              {formatNumber(statistic.mercanto)}
            </td>
            <td className="border border-gray-300 px-4 py-2 text-center">
              {formatNumber(statistic.merchantConfirmed)}
            </td>
            <td
              className={`border border-gray-300 px-4 py-2 text-center ${
                statistic.merchantOutstanding > 0 ? 'bg-yellow-100' : ''
              }`}
            >
              {formatNumber(statistic.merchantOutstanding)}
            </td>
            <td
              className={`border border-gray-300 px-4 py-2 text-center ${
                statistic.merchantDifference < 0 ? 'bg-red-100' : ''
              }`}
            >
              {formatNumber(statistic.merchantDifference)}
            </td>
            <td
              className={`border border-gray-300 px-4 py-2 text-center ${
                statistic.merchantCancelled > 0 ? 'bg-red-100' : ''
              }`}
            >
              {formatNumber(statistic.merchantCancelled)}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  )

  return (
    <main>
      <PageHeading backLink={{ title: 'Sales Orders', href: backUrl }}>
        Delivery Comparison for reference &quot;{reference}&quot;
      </PageHeading>

      <div className="mb-6">
        <DetailTable params={headerParams} />
      </div>

      <div className="mb-8">
        <h2 className="mb-4 text-center text-xl font-bold">
          Cumulative Information
        </h2>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
          {renderStatisticTable('Positions', header.nbItems)}
          {renderStatisticTable('Quantity', header.quantity)}
          {renderStatisticTable('Price [CHF]', header.price)}
          {renderStatisticTable('Net Amount [CHF]', header.netAmount)}
          {renderStatisticTable('Gross Amount [CHF]', header.grossAmount)}
        </div>
      </div>

      <div className="mb-6">
        <h2 className="mb-4 text-center text-xl font-bold">Items Listing</h2>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse border border-gray-300 text-sm">
            <thead>
              <tr className="bg-gray-100">
                <th className="border border-gray-300 px-2 py-2">Status</th>
                <th className="border border-gray-300 px-2 py-2">Item ID</th>
                <th className="border border-gray-300 px-2 py-2">SKU</th>
                <th className="border border-gray-300 px-2 py-2">Order Date</th>
                <th className="border border-gray-300 px-2 py-2">
                  Delivery Date
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Delivery Method
                </th>
                <th className="border border-gray-300 px-2 py-2">Origin</th>
                <th className="border border-gray-300 px-2 py-2">
                  Base Price Unit
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Base Price [CHF]
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Packaging / Measurement Unit
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Packaging / Measurement Code
                </th>
                <th className="border border-gray-300 px-2 py-2">Packaging</th>
                <th className="border border-gray-300 px-2 py-2">Quantity</th>
                <th className="border border-gray-300 px-2 py-2">
                  Price [CHF]
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Net Amount [CHF]
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Gross Amount [CHF]
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Tax Rate [%]
                </th>
                <th className="border border-gray-300 px-2 py-2">
                  Last Update
                </th>
              </tr>
            </thead>
            <tbody>
              {positions.map((position, index) => {
                if (position.status === 'Confirmed Additional') {
                  return (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {position.status}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {position.itemId}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {position.concreteSku}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {formatDateDisplay(position.orderDate)}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                        {position.deliveryDateMerchant
                          ? formatDateDisplay(position.deliveryDateMerchant)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                        {position.deliveryMethodMerchant || '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                        {position.merchant}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.basePriceQuantityMerchant || '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.basePriceMerchant
                          ? formatNumber(position.basePriceMerchant)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.basePackagingCodeMerchant ||
                          position.measurementQuantityMerchant ||
                          '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                        {position.basePackagingCodeMerchant ||
                          position.baseMeasurementCodeMerchant ||
                          '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                        {position.packagingMerchant || '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.quantityMerchant || '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.priceMerchant
                          ? formatNumber(position.priceMerchant)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.netAmountMerchant
                          ? formatNumber(position.netAmountMerchant)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.grossAmountMerchant
                          ? formatNumber(position.grossAmountMerchant)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 bg-gray-100 px-2 py-2 text-right">
                        {position.taxRateMerchant
                          ? formatNumber(position.taxRateMerchant * 100)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {formatDateTimeDisplay(position.lastUpdate)}
                      </td>
                    </tr>
                  )
                }

                // Regular two-row format
                return (
                  <React.Fragment key={index}>
                    {/* Mercanto row */}
                    <tr className="hover:bg-gray-50">
                      <td
                        rowSpan={2}
                        className="border border-gray-300 px-2 py-2 text-center align-middle"
                      >
                        {position.status}
                      </td>
                      <td
                        rowSpan={2}
                        className="border border-gray-300 px-2 py-2 text-center align-middle"
                      >
                        {position.itemId}
                      </td>
                      <td
                        rowSpan={2}
                        className="border border-gray-300 px-2 py-2 text-center align-middle"
                      >
                        {position.concreteSku}
                      </td>
                      <td
                        rowSpan={2}
                        className="border border-gray-300 px-2 py-2 text-center align-middle"
                      >
                        {formatDateDisplay(position.orderDate)}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-center">
                        {position.deliveryDateMercanto
                          ? formatDateDisplay(position.deliveryDateMercanto)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2">
                        {position.deliveryMethodMercanto || '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2">
                        mercanto
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.basePriceQuantityMercanto || '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.basePriceMercanto
                          ? formatNumber(position.basePriceMercanto)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.basePackagingCodeMercanto ||
                          position.measurementQuantityMercanto ||
                          '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2">
                        {position.basePackagingCodeMercanto ||
                          position.baseMeasurementCodeMercanto ||
                          '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2">
                        {position.packagingMercanto || '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.quantityMercanto || '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.priceMercanto
                          ? formatNumber(position.priceMercanto)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.netAmountMercanto
                          ? formatNumber(position.netAmountMercanto)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.grossAmountMercanto
                          ? formatNumber(position.grossAmountMercanto)
                          : '-'}
                      </td>
                      <td className="border border-gray-300 px-2 py-2 text-right">
                        {position.taxRateMercanto
                          ? formatNumber(position.taxRateMercanto * 100)
                          : '-'}
                      </td>
                      <td
                        rowSpan={2}
                        className="border border-gray-300 px-2 py-2 text-center align-middle"
                      >
                        {position.confirmationReceived
                          ? formatDateTimeDisplay(position.lastUpdate)
                          : '-'}
                      </td>
                    </tr>
                    {/* Merchant row or "Position Not Confirmed Yet" */}
                    {!position.confirmationReceived ? (
                      <tr className="border-b border-gray-800">
                        <td
                          colSpan={13}
                          className="border border-gray-300 bg-gray-100 px-2 py-2 text-center"
                        >
                          Position Not Confirmed Yet
                        </td>
                      </tr>
                    ) : (
                      <tr className="border-b border-gray-800">
                        <td
                          className={`border border-gray-300 px-2 py-2 text-center ${
                            position.cancelled ||
                            position.deliveryDateMercanto !==
                              position.deliveryDateMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.cancelled
                            ? 'Cancelled'
                            : position.deliveryDateMerchant
                              ? formatDateDisplay(position.deliveryDateMerchant)
                              : '-'}
                        </td>
                        <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                          {position.deliveryMethodMerchant || '-'}
                        </td>
                        <td className="border border-gray-300 bg-gray-100 px-2 py-2">
                          {position.merchant}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.basePriceQuantityMercanto !==
                            position.basePriceQuantityMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.basePriceQuantityMerchant || '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.basePriceMercanto !==
                            position.basePriceMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.basePriceMerchant
                            ? formatNumber(position.basePriceMerchant)
                            : '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            (position.basePackagingCodeMercanto ||
                              position.measurementQuantityMercanto) !==
                            (position.basePackagingCodeMerchant ||
                              position.measurementQuantityMerchant)
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.basePackagingCodeMerchant ||
                            position.measurementQuantityMerchant ||
                            '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 ${
                            (position.basePackagingCodeMercanto ||
                              position.baseMeasurementCodeMercanto) !==
                            (position.basePackagingCodeMerchant ||
                              position.baseMeasurementCodeMerchant)
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.basePackagingCodeMerchant ||
                            position.baseMeasurementCodeMerchant ||
                            '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 ${
                            position.packagingMercanto !==
                            position.packagingMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.packagingMerchant || '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.cancelled ||
                            position.quantityMercanto !==
                              position.quantityMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.cancelled
                            ? '0'
                            : position.quantityMerchant || '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.cancelled ||
                            position.priceMercanto !== position.priceMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.cancelled
                            ? formatNumber(0)
                            : position.priceMerchant
                              ? formatNumber(position.priceMerchant)
                              : '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.cancelled ||
                            position.netAmountMercanto !==
                              position.netAmountMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.cancelled
                            ? formatNumber(0)
                            : position.netAmountMerchant
                              ? formatNumber(position.netAmountMerchant)
                              : '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.cancelled ||
                            position.grossAmountMercanto !==
                              position.grossAmountMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.cancelled
                            ? formatNumber(0)
                            : position.grossAmountMerchant
                              ? formatNumber(position.grossAmountMerchant)
                              : '-'}
                        </td>
                        <td
                          className={`border border-gray-300 px-2 py-2 text-right ${
                            position.taxRateMercanto !==
                            position.taxRateMerchant
                              ? 'bg-red-100'
                              : 'bg-gray-100'
                          }`}
                        >
                          {position.taxRateMerchant
                            ? formatNumber(position.taxRateMerchant * 100)
                            : '-'}
                        </td>
                      </tr>
                    )}
                  </React.Fragment>
                )
              })}
            </tbody>
          </table>
        </div>
      </div>
    </main>
  )
}
