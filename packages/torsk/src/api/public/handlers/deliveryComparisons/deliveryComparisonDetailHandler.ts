import { DeliveryComparisonDetailType } from '@mercanto/api-types'
import { processDependencyContainer } from '@mercanto/basis/dependencies.js'
import { FastifyInstance, FastifyReply, FastifyRequest } from 'fastify'
import { DeliveryComparisonRepository } from '../../../../database/Repository/DeliveryComparisonRepository.js'

export async function deliveryComparisonDetailHandler(
  this: FastifyInstance,
  request: FastifyRequest<{ Params: { reference: string } }>,
  reply: FastifyReply
): Promise<DeliveryComparisonDetailType> {
  const logger = request.log
  const deliveryComparisonRepository = processDependencyContainer.resolve(
    DeliveryComparisonRepository
  )

  const deliveryComparison = await deliveryComparisonRepository.getEntity({
    reference: request.params.reference
  })

  if (!deliveryComparison) {
    logger.warn(
      { reference: request.params.reference },
      'Delivery Comparison not found'
    )
    return reply.status(404).send()
  }

  logger.info('Returning delivery comparison...')
  return deliveryComparison
}
