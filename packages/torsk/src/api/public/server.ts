import type http from 'node:http'
import cors from '@fastify/cors'
import { getFastifyInstrumentationPlugin } from '@mercanto/open-telemetry'
import ajvErrors from 'ajv-errors'
import fastify, { FastifyInstance } from 'fastify'
import {
  serializerCompiler,
  validator<PERSON><PERSON>piler,
  ZodTypeProvider
} from 'fastify-type-provider-zod'
import { z } from 'zod'
import { extendZodWithOpenApi } from 'zod-openapi'
import { dependencyContainerDecorator } from '../common/plugins/dependencyContainerDecorator.js'
import { authMiddleware } from './authMiddleware.js'
import { configProfilesV1 } from './plugins/routes/configProfilesV1.js'
import { customerInvitationsV1 } from './plugins/routes/customerInvitationsV1.js'
import { dataArchiveEntitiesV1 } from './plugins/routes/dataArchiveEntitiesV1.js'
import { dataArchiveEntityLinksV1 } from './plugins/routes/dataArchiveEntityLinksV1.js'
import { deliveryComparisonsV1 } from './plugins/routes/deliveryComparisonsV1.js'
import { filtersV1 } from './plugins/routes/filtersV1.js'
import { merchantRelationshipsV1 } from './plugins/routes/merchantRelationshipsV1.js'
import { merchantsV1 } from './plugins/routes/merchantsV1.js'
import { orderDeadlinesV1 } from './plugins/routes/orderDeadlinesV1.js'
import { productAdditionalFieldsV1 } from './plugins/routes/productAdditionalFieldsV1.js'
import { productRelationsV1 } from './plugins/routes/productRelationsV1.js'
import { profileConfigTemplatesV1 } from './plugins/routes/profileConfigTemplatesV1.js'
import { salesOrdersV1 } from './plugins/routes/salesOrdersV1.js'
import { shoppingListsV1 } from './plugins/routes/shoppingListsV1.js'

export async function getPublicServer(): Promise<FastifyInstance<http.Server>> {
  const server = fastify<http.Server>({
    logger: { level: process.env.LOG_LEVEL ?? 'info' },
    ajv: {
      customOptions: {
        allErrors: true,
        // necessary since we're passing generic JSON it needs to match exactly or error
        removeAdditional: false,
        strict: true
      },
      plugins: [ajvErrors as unknown as Function | [Function, unknown]]
    },
    // Trust localhost proxy as well as the subnet Rancher routers use.
    trustProxy: '127.0.0.1,********/16',
    maxParamLength: 500
  })

  await server.register(cors, {
    origin: process.env.CORS_ALLOWED_URLS?.split(',') ?? false,
    methods: ['GET', 'POST', 'PUT']
  })

  // Skip metrics plug-in when running tests.
  if (process.env.NODE_ENV !== 'test') {
    await server.register(getFastifyInstrumentationPlugin())
  }

  server.addHook('preHandler', authMiddleware)

  server.addHook('preHandler', async (request, _reply) => {
    if (request.body) {
      request.log.info({ body: request.body }, 'Request body')
    }
  })

  await server.register(import('@fastify/rate-limit'), {
    max: 100,
    timeWindow: '1 minute'
  })

  extendZodWithOpenApi(z)

  server.setValidatorCompiler(validatorCompiler)
  server.setSerializerCompiler(serializerCompiler)

  // See https://www.fastify.io/docs/latest/Reference/TypeScript/#fastify-type-providers
  server.withTypeProvider<ZodTypeProvider>()

  server.get('/heartbeat', (_request, reply) => {
    void reply.send({ status: 'ok' })
  })

  // Load decorators first.
  // For some reason, Jetbrains complains when registering this plugin
  // while TypeScript does not. So, just ignore the wiggly red lines.
  void server.register(dependencyContainerDecorator)

  void server.register(merchantsV1)
  void server.register(configProfilesV1)
  void server.register(customerInvitationsV1)
  void server.register(dataArchiveEntitiesV1)
  void server.register(dataArchiveEntityLinksV1)
  void server.register(deliveryComparisonsV1)
  void server.register(filtersV1)
  void server.register(merchantRelationshipsV1)
  void server.register(orderDeadlinesV1)
  void server.register(productAdditionalFieldsV1)
  void server.register(productRelationsV1)
  void server.register(profileConfigTemplatesV1)
  void server.register(salesOrdersV1)
  void server.register(shoppingListsV1)

  return server
}
