import {
  deliveryComparisonDetailSchema
} from '@mercanto/api-types'
import type { FastifyInstance, FastifyPluginOptions } from 'fastify'
import { deliveryComparisonDetailHandler } from '../../handlers/deliveryComparisons/deliveryComparisonDetailHandler.js'

export const deliveryComparisonsV1 = (
  fastify: FastifyInstance,
  _opts: FastifyPluginOptions,
  done: (err?: Error) => void
): void => {
  fastify.get<{ Params: { reference: string } }>(
    '/v1/delivery-comparison/:reference',
    {
      schema: {
        response: {
          200: deliveryComparisonDetailSchema
        }
      }
    },
    deliveryComparisonDetailHandler
  )

  done()
}
