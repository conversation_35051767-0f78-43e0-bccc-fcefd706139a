import {
  DependencyContainer,
  dependencyRegistry
} from '@mercanto/basis/dependencies.js'
import { PostgresPool } from '../PostgresPool.js'
import type { ReadableRepository } from '../Repository.js'
import {
  SalesOrderRepository,
  LoadedSalesOrderEntity
} from './SalesOrderRepository.js'
import {
  DeliveryConfirmationRepository,
  LoadedDeliveryConfirmationEntity
} from './DeliveryConfirmationRepository.js'
import {
  MetricsSalesOrderRepository,
  LoadedMetricsSalesOrderEntity
} from './MetricsSalesOrderRepository.js'
import type {
  DeliveryComparisonDetailType,
  DeliveryComparisonHeaderType,
  DeliveryComparisonPositionType,
  DeliveryComparisonStatisticType
} from '@mercanto/api-types'

export interface DeliveryComparisonEntityKey {
  reference: string
}

export class DeliveryComparisonRepository
  implements
    ReadableRepository<
      DeliveryComparisonEntityKey,
      DeliveryComparisonDetailType
    >
{
  private readonly pool: PostgresPool
  private readonly salesOrderRepository: SalesOrderRepository
  private readonly deliveryConfirmationRepository: DeliveryConfirmationRepository
  private readonly metricsSalesOrderRepository: MetricsSalesOrderRepository

  public constructor(dc: DependencyContainer) {
    this.pool = dc.resolve(PostgresPool)
    this.salesOrderRepository = dc.resolve(SalesOrderRepository)
    this.deliveryConfirmationRepository = dc.resolve(
      DeliveryConfirmationRepository
    )
    this.metricsSalesOrderRepository = dc.resolve(MetricsSalesOrderRepository)
  }

  public async getEntity(
    entityKey: DeliveryComparisonEntityKey
  ): Promise<DeliveryComparisonDetailType | undefined> {
    // Get the metrics sales order to find the merchant
    const metricsSalesOrder = await this.metricsSalesOrderRepository.getEntity({
      reference: entityKey.reference
    })

    if (!metricsSalesOrder) {
      return undefined
    }

    // Get the public sales order
    const publicSalesOrder = await this.getPublicSalesOrder(
      metricsSalesOrder.merchant,
      entityKey.reference
    )

    if (!publicSalesOrder) {
      return {
        isValid: false,
        header: null,
        positions: null
      }
    }

    // Check if it's a valid IoCanto sales order
    const isValid = this.isValidSalesOrderIoCantoJson(publicSalesOrder.document)

    if (!isValid) {
      return {
        isValid: false,
        header: null,
        positions: null
      }
    }

    // Get delivery confirmations
    const deliveryConfirmations = await this.getDeliveryConfirmations(
      metricsSalesOrder.delivery_confirmation_ids || []
    )

    // Compute delivery comparison
    const { header, positions } = await this.computeDeliveryComparison(
      metricsSalesOrder,
      publicSalesOrder.document,
      deliveryConfirmations
        .map((dc) => dc.document)
        .filter((doc) => doc !== null)
    )

    return {
      isValid: true,
      header,
      positions
    }
  }

  private async getPublicSalesOrder(
    merchant: string,
    reference: string
  ): Promise<LoadedSalesOrderEntity | undefined> {
    return await this.salesOrderRepository.getEntity({ merchant, reference })
  }

  private async getDeliveryConfirmations(
    deliveryConfirmationIds: number[]
  ): Promise<LoadedDeliveryConfirmationEntity[]> {
    if (deliveryConfirmationIds.length === 0) {
      return []
    }

    const result = await this.pool.query<LoadedDeliveryConfirmationEntity>(
      `
        SELECT * FROM delivery_confirmations
        WHERE id = ANY($1::int[])
      `,
      [deliveryConfirmationIds]
    )

    return result.rows
  }

  private isValidSalesOrderIoCantoJson(document: any): boolean {
    return (
      document &&
      typeof document === 'object' &&
      document.customer_key &&
      document.order_reference &&
      Array.isArray(document.line_items)
    )
  }

  private async computeDeliveryComparison(
    salesOrder: LoadedMetricsSalesOrderEntity,
    ioCantoSalesOrder: any,
    ioCantoDeliveryConfirmations: any[]
  ): Promise<{
    header: DeliveryComparisonHeaderType
    positions: DeliveryComparisonPositionType[]
  }> {
    // Extract delivery confirmation line items
    const lineItemsDc = new Map<string, any>()

    for (const dc of ioCantoDeliveryConfirmations) {
      if (dc.line_items) {
        for (const lineItem of dc.line_items) {
          if (lineItem.order_reference === ioCantoSalesOrder.order_reference) {
            const extendedLineItem = {
              ...lineItem,
              customer_key: dc.customer_key,
              confirmation_received: true,
              delivery_date: dc.delivery_date,
              delivery_method: dc.delivery_method,
              delete: lineItem.delete === true,
              timestamp: dc.timestamp
            }
            lineItemsDc.set(lineItem.item_id, extendedLineItem)
          }
        }
      }
    }

    // Create line items map from sales order
    const lineItemsSo = new Map<string, any>()
    if (ioCantoSalesOrder.line_items) {
      for (const lineItem of ioCantoSalesOrder.line_items) {
        lineItemsSo.set(lineItem.item_id, lineItem)
      }
    }

    const itemIds =
      ioCantoSalesOrder.line_items?.map((item: any) => item.item_id) || []

    // Create expected positions
    const expectedPositions: DeliveryComparisonPositionType[] = []

    for (const itemId of itemIds) {
      const so = lineItemsSo.get(itemId)
      const dc = lineItemsDc.get(itemId) || {}

      const status = this.getStatus(
        dc.confirmation_received || false,
        dc.delete || false,
        false
      )

      const grossAmountMercanto =
        so.net_amount && so.tax_rate
          ? this.toFrancs(
              so.net_amount + so.net_amount * this.toPercentage(so.tax_rate)
            )
          : null

      expectedPositions.push({
        merchant: salesOrder.merchant,
        status,
        additional: false,
        cancelled: dc.delete || false,
        confirmationReceived: dc.confirmation_received || false,
        orderReference: dc.order_reference || '',
        supplierOrderReference: dc.supplier_order_reference || null,
        itemId: dc.item_id || '',
        concreteSku: dc.concrete_sku || '',
        orderDate: ioCantoSalesOrder.order_date,
        deliveryDateMercanto: so?.delivery_date || null,
        deliveryDateMerchant: dc.delivery_date || null,
        deliveryMethodMercanto: ioCantoSalesOrder.delivery_method || null,
        deliveryMethodMerchant: dc.delivery_method || null,
        packagingMercanto: so?.packaging || null,
        packagingMerchant: dc.packaging || null,
        basePriceMercanto: so?.base_price ? this.toFrancs(so.base_price) : null,
        basePriceMerchant: dc.base_price ? this.toFrancs(dc.base_price) : null,
        basePriceQuantityMercanto: so?.base_price_quantity || null,
        basePriceQuantityMerchant: dc.base_price_quantity || null,
        baseQuantityMercanto: so?.base_quantity || null,
        baseQuantityMerchant: dc.base_quantity || null,
        basePackagingCodeMercanto: so?.base_packaging_code || null,
        basePackagingCodeMerchant: dc.base_packaging_code || null,
        packagingBaseQuantityMercanto: so?.packaging_base_quantity || null,
        packagingBaseQuantityMerchant: dc.packaging_base_quantity || null,
        baseMeasurementCodeMercanto: so?.base_measurement_code || null,
        baseMeasurementCodeMerchant: dc.base_measurement_code || null,
        measurementQuantityMercanto: so?.measurement_quantity || null,
        measurementQuantityMerchant: dc.measurement_quantity || null,
        priceMercanto: so?.price ? this.toFrancs(so.price) : null,
        priceMerchant: dc.price ? this.toFrancs(dc.price) : null,
        quantityMercanto: so?.quantity || null,
        quantityMerchant: dc.quantity || null,
        netAmountMercanto: so?.net_amount ? this.toFrancs(so.net_amount) : null,
        netAmountMerchant: dc.net_amount ? this.toFrancs(dc.net_amount) : null,
        taxRateMercanto: so?.tax_rate ? this.toPercentage(so.tax_rate) : null,
        taxRateMerchant: dc.tax_rate ? this.toPercentage(dc.tax_rate) : null,
        grossAmountMercanto,
        grossAmountMerchant:
          dc.net_amount && dc.tax_rate
            ? this.toFrancs(
                dc.net_amount + dc.net_amount * this.toPercentage(dc.tax_rate)
              )
            : null,
        lastUpdate: dc.timestamp || new Date().toISOString()
      })
    }

    // Create additional positions (items in delivery confirmation but not in sales order)
    const additionalPositions: DeliveryComparisonPositionType[] = []

    for (const [itemId, dc] of lineItemsDc) {
      if (!itemIds.includes(itemId)) {
        const status = this.getStatus(
          dc.confirmation_received || false,
          dc.delete || false,
          true
        )

        additionalPositions.push({
          merchant: salesOrder.merchant,
          status,
          additional: true,
          cancelled: dc.delete || false,
          confirmationReceived: dc.confirmation_received || false,
          orderReference: dc.order_reference || '',
          supplierOrderReference: dc.supplier_order_reference || null,
          itemId: dc.item_id || '',
          concreteSku: dc.concrete_sku || '',
          orderDate: ioCantoSalesOrder.order_date,
          deliveryDateMercanto: null,
          deliveryDateMerchant: dc.delivery_date || null,
          deliveryMethodMercanto: null,
          deliveryMethodMerchant: dc.delivery_method || null,
          packagingMercanto: null,
          packagingMerchant: dc.packaging || null,
          basePriceMercanto: null,
          basePriceMerchant: dc.base_price
            ? this.toFrancs(dc.base_price)
            : null,
          basePriceQuantityMercanto: null,
          basePriceQuantityMerchant: dc.base_price_quantity || null,
          baseQuantityMercanto: null,
          baseQuantityMerchant: dc.base_quantity || null,
          basePackagingCodeMercanto: null,
          basePackagingCodeMerchant: dc.base_packaging_code || null,
          packagingBaseQuantityMercanto: null,
          packagingBaseQuantityMerchant: dc.packaging_base_quantity || null,
          baseMeasurementCodeMercanto: null,
          baseMeasurementCodeMerchant: dc.base_measurement_code || null,
          measurementQuantityMercanto: null,
          measurementQuantityMerchant: dc.measurement_quantity || null,
          priceMercanto: null,
          priceMerchant: dc.price ? this.toFrancs(dc.price) : null,
          quantityMercanto: null,
          quantityMerchant: dc.quantity || null,
          netAmountMercanto: null,
          netAmountMerchant: dc.net_amount
            ? this.toFrancs(dc.net_amount)
            : null,
          taxRateMercanto: null,
          taxRateMerchant: dc.tax_rate ? this.toPercentage(dc.tax_rate) : null,
          grossAmountMercanto: null,
          grossAmountMerchant:
            dc.net_amount && dc.tax_rate
              ? this.toFrancs(
                  dc.net_amount + dc.net_amount * this.toPercentage(dc.tax_rate)
                )
              : null,
          lastUpdate: dc.timestamp || new Date().toISOString()
        })
      }
    }

    const allPositions = [...expectedPositions, ...additionalPositions]

    // Compute aggregates
    const aggregates = this.computeAggregates(allPositions)

    const header: DeliveryComparisonHeaderType = {
      merchant: salesOrder.merchant,
      customerKey: ioCantoSalesOrder.customer_key,
      customerType: salesOrder.customer_type || 'unknown',
      source: salesOrder.source || 'unknown',
      orderReference: salesOrder.reference,
      orderDate: ioCantoSalesOrder.order_date,
      nbItems: aggregates.nbItems,
      quantity: aggregates.quantity,
      price: aggregates.price,
      netAmount: aggregates.netAmount,
      grossAmount: aggregates.grossAmount,
      lastUpdate: aggregates.lastUpdate
    }

    return { header, positions: allPositions }
  }

  private computeAggregates(positions: DeliveryComparisonPositionType[]): {
    nbItems: DeliveryComparisonStatisticType
    quantity: DeliveryComparisonStatisticType
    price: DeliveryComparisonStatisticType
    netAmount: DeliveryComparisonStatisticType
    grossAmount: DeliveryComparisonStatisticType
    lastUpdate: string
  } {
    let lastUpdate = new Date(0).toISOString()

    const nbItems = this.createEmptyStatistic()
    const quantity = this.createEmptyStatistic()
    const price = this.createEmptyStatistic()
    const netAmount = this.createEmptyStatistic()
    const grossAmount = this.createEmptyStatistic()

    for (const pos of positions) {
      if (pos.lastUpdate > lastUpdate) {
        lastUpdate = pos.lastUpdate
      }

      // Update statistics
      this.updateStatistic(nbItems, {
        mercanto: this.getPosMercanto(pos),
        merchant: 1,
        cancelled: pos.cancelled,
        confirmationReceived: pos.confirmationReceived
      })

      this.updateStatistic(quantity, {
        mercanto: pos.quantityMercanto || 0,
        merchant: pos.quantityMerchant || 0,
        cancelled: pos.cancelled,
        confirmationReceived: pos.confirmationReceived
      })

      this.updateStatistic(price, {
        mercanto: pos.priceMercanto || 0,
        merchant: pos.priceMerchant || 0,
        cancelled: pos.cancelled,
        confirmationReceived: pos.confirmationReceived
      })

      this.updateStatistic(netAmount, {
        mercanto: pos.netAmountMercanto || 0,
        merchant: pos.netAmountMerchant || 0,
        cancelled: pos.cancelled,
        confirmationReceived: pos.confirmationReceived
      })

      this.updateStatistic(grossAmount, {
        mercanto: pos.grossAmountMercanto || 0,
        merchant: pos.grossAmountMerchant || 0,
        cancelled: pos.cancelled,
        confirmationReceived: pos.confirmationReceived
      })
    }

    return { nbItems, quantity, price, netAmount, grossAmount, lastUpdate }
  }

  private createEmptyStatistic(): DeliveryComparisonStatisticType {
    return {
      mercanto: 0,
      merchantConfirmed: 0,
      merchantOutstanding: 0,
      merchantCancelled: 0,
      merchantDifference: 0
    }
  }

  private updateStatistic(
    statistic: DeliveryComparisonStatisticType,
    values: {
      mercanto: number
      merchant: number
      cancelled: boolean
      confirmationReceived: boolean
    }
  ): void {
    statistic.mercanto += values.mercanto

    if (values.confirmationReceived && !values.cancelled) {
      statistic.merchantConfirmed += values.merchant
    }

    if (!values.confirmationReceived) {
      statistic.merchantOutstanding += values.mercanto
    }

    if (values.confirmationReceived && values.cancelled) {
      statistic.merchantCancelled += values.merchant
    }

    statistic.merchantDifference =
      statistic.merchantConfirmed +
      statistic.merchantCancelled -
      statistic.mercanto
  }

  private getPosMercanto(pos: DeliveryComparisonPositionType): number {
    if (pos.additional) {
      return 0
    }
    return 1
  }

  private getStatus(
    confirmationReceived: boolean,
    deleted: boolean,
    additional: boolean
  ): string {
    if (!confirmationReceived) {
      return 'Outstanding'
    }
    if (deleted && additional) {
      return 'Cancelled Additional'
    }
    if (deleted) {
      return 'Cancelled'
    }
    if (additional) {
      return 'Confirmed Additional'
    }
    return 'Confirmed'
  }

  private toFrancs(value: number): number {
    return value / 100000
  }

  private toPercentage(value: number): number {
    return value / 1000000
  }
}

dependencyRegistry.set(DeliveryComparisonRepository, {
  useClass: DeliveryComparisonRepository,
  scope: 'process'
})
