import { describe, it, expect, beforeEach, afterEach } from 'vitest'
import type { AppContext } from '../../../app/AppContext.js'
import { DeliveryComparisonRepository } from '../../../database/Repository/DeliveryComparisonRepository.js'
import { mockAppContext } from '../../util/mockAppContext.js'

describe('database/Repository/DeliveryComparisonRepository', () => {
  let context: AppContext

  beforeEach(async () => {
    context = await mockAppContext()
  })

  afterEach(async () => {
    await context.job.end()
  })

  it('should return undefined for non-existent sales order', async () => {
    const repository = context.job.container.resolve(
      DeliveryComparisonRepository
    )

    const result = await repository.getEntity({
      reference: 'NON_EXISTENT_ORDER'
    })

    expect(result).toBeUndefined()
  })

  it('should return invalid comparison for invalid sales order format', async () => {
    // This test would require setting up test data
    // For now, we just test the basic structure
    const repository = context.job.container.resolve(
      DeliveryComparisonRepository
    )

    expect(repository).toBeDefined()
    expect(typeof repository.getEntity).toBe('function')
  })
})
