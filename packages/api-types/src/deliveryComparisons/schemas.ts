import { z } from 'zod'
import { timestampType, dateType } from '../common/index.js'

export const deliveryComparisonStatisticSchema = z.object({
  mercanto: z.number(),
  merchantConfirmed: z.number(),
  merchantOutstanding: z.number(),
  merchantCancelled: z.number(),
  merchantDifference: z.number()
})

export type DeliveryComparisonStatisticType = z.infer<typeof deliveryComparisonStatisticSchema>

export const deliveryComparisonHeaderSchema = z.object({
  merchant: z.string().openapi({ example: 'pistor' }),
  customerKey: z.string().openapi({ example: '199483' }),
  customerType: z.string().openapi({ example: 'regular' }),
  source: z.string().openapi({ example: 'web' }),
  orderReference: z.string().openapi({ example: 'M-59373' }),
  orderDate: dateType.openapi({ example: '2023-06-01' }),
  nbItems: deliveryComparisonStatisticSchema,
  quantity: deliveryComparisonStatisticSchema,
  price: deliveryComparisonStatisticSchema,
  netAmount: deliveryComparisonStatisticSchema,
  grossAmount: deliveryComparisonStatisticSchema,
  lastUpdate: timestampType.openapi({ example: '2023-06-01T12:00:00Z' })
})

export type DeliveryComparisonHeaderType = z.infer<typeof deliveryComparisonHeaderSchema>

export const deliveryComparisonPositionSchema = z.object({
  merchant: z.string().openapi({ example: 'pistor' }),
  status: z.string().openapi({ example: 'Confirmed' }),
  additional: z.boolean(),
  cancelled: z.boolean(),
  confirmationReceived: z.boolean(),
  orderReference: z.string().openapi({ example: 'M-59373' }),
  supplierOrderReference: z.string().nullable(),
  itemId: z.string().openapi({ example: 'item-123' }),
  concreteSku: z.string().openapi({ example: 'SKU-123' }),
  orderDate: dateType.openapi({ example: '2023-06-01' }),
  deliveryDateMercanto: dateType.nullable(),
  deliveryDateMerchant: dateType.nullable(),
  deliveryMethodMercanto: z.string().nullable(),
  deliveryMethodMerchant: z.string().nullable(),
  packagingMercanto: z.string().nullable(),
  packagingMerchant: z.string().nullable(),
  basePriceMercanto: z.number().nullable(),
  basePriceMerchant: z.number().nullable(),
  basePriceQuantityMercanto: z.number().nullable(),
  basePriceQuantityMerchant: z.number().nullable(),
  baseQuantityMercanto: z.number().nullable(),
  baseQuantityMerchant: z.number().nullable(),
  basePackagingCodeMercanto: z.string().nullable(),
  basePackagingCodeMerchant: z.string().nullable(),
  packagingBaseQuantityMercanto: z.number().nullable(),
  packagingBaseQuantityMerchant: z.number().nullable(),
  baseMeasurementCodeMercanto: z.string().nullable(),
  baseMeasurementCodeMerchant: z.string().nullable(),
  measurementQuantityMercanto: z.number().nullable(),
  measurementQuantityMerchant: z.number().nullable(),
  priceMercanto: z.number().nullable(),
  priceMerchant: z.number().nullable(),
  quantityMercanto: z.number().nullable(),
  quantityMerchant: z.number().nullable(),
  netAmountMercanto: z.number().nullable(),
  netAmountMerchant: z.number().nullable(),
  taxRateMercanto: z.number().nullable(),
  taxRateMerchant: z.number().nullable(),
  grossAmountMercanto: z.number().nullable(),
  grossAmountMerchant: z.number().nullable(),
  lastUpdate: timestampType.openapi({ example: '2023-06-01T12:00:00Z' })
})

export type DeliveryComparisonPositionType = z.infer<typeof deliveryComparisonPositionSchema>

export const deliveryComparisonDetailSchema = z.object({
  isValid: z.boolean(),
  header: deliveryComparisonHeaderSchema.nullable(),
  positions: z.array(deliveryComparisonPositionSchema).nullable()
})

export type DeliveryComparisonDetailType = z.infer<typeof deliveryComparisonDetailSchema>
